# Supported Programming Languages

## Overview

The code analysis system supports **45 programming languages** across **76 file extensions**, organized into **13 categories** with both **specialized** and **generic** processors.

## Quick Stats

- **Total Languages**: 45
- **Total File Extensions**: 76  
- **Specialized Processors**: 9 (high-priority, optimized parsing)
- **Generic Processors**: 36 (universal parsing with Tree-sitter support where available)
- **Tree-sitter Support**: 25 languages have dedicated Tree-sitter parsers

## Language Categories

### 🔧 Systems Programming (4 languages)
High-performance, compiled languages for system-level development:
- **C/C++** (.c, .cpp, .h, .hpp, etc.) - *Specialized processor*
- **Rust** (.rs) - *Specialized processor*  
- **Go** (.go) - *Specialized processor*
- **Swift** (.swift) - *Generic processor*

### 🐍 Scripting Languages (5 languages)
Interpreted languages for automation and rapid development:
- **Python** (.py, .pyw) - *Specialized processor*
- **Ruby** (.rb) - *Generic processor*
- **Perl** (.pl, .pm) - *Generic processor*
- **Lua** (.lua) - *Generic processor*
- **Bash** (.sh, .bash) - *Generic processor*

### 🌐 Web Technologies (6 languages)
Languages for web development and frontend/backend:
- **JavaScript** (.js, .mjs) - *Specialized processor*
- **TypeScript** (.ts, .tsx) - *Specialized processor*
- **HTML** (.html, .htm) - *Generic processor*
- **CSS** (.css) - *Generic processor*
- **SCSS** (.scss, .sass) - *Generic processor*
- **PHP** (.php) - *Generic processor*

### ☕ JVM Languages (4 languages)
Languages that run on the Java Virtual Machine:
- **Java** (.java) - *Specialized processor*
- **Kotlin** (.kt, .kts) - *Generic processor*
- **Scala** (.scala) - *Generic processor*
- **Groovy** (.groovy) - *Generic processor*

### 🧮 Functional Languages (6 languages)
Languages emphasizing functional programming paradigms:
- **Haskell** (.hs) - *Generic processor*
- **Erlang** (.erl) - *Generic processor*
- **Elixir** (.ex, .exs) - *Generic processor*
- **Clojure** (.clj, .cljs) - *Generic processor*
- **Lisp** (.lisp, .lsp) - *Generic processor*
- **Scheme** (.scm, .ss) - *Generic processor*

### 🤖 AI/Logic Programming (2 languages)
Languages for artificial intelligence and logic programming:
- **Metta** (.metta, .mta) - *Specialized processor* (OpenCog Hyperon AGI)
- **Prolog** (.pl, .pro, .prolog) - *Generic processor*

### 🔌 Hardware Description Languages (2 languages)
Languages for digital circuit design and hardware modeling:
- **Verilog** (.v, .vh) - *Generic processor*
- **VHDL** (.vhd, .vhdl) - *Generic processor*

### 📊 Data Languages (5 languages)
Languages for data interchange and configuration:
- **SQL** (.sql) - *Generic processor*
- **JSON** (.json) - *Generic processor*
- **YAML** (.yaml, .yml) - *Generic processor*
- **XML** (.xml) - *Generic processor*
- **TOML** (.toml) - *Generic processor*

### 🔬 Scientific Computing (3 languages)
Languages for mathematical and scientific computation:
- **MATLAB** (.m) - *Generic processor*
- **R** (.R, .r) - *Generic processor*
- **Fortran** (.f, .f90, .f95) - *Generic processor*

### 🏗️ Build Systems (2 languages)
Languages and formats for build automation:
- **Makefile** (.mk, Makefile) - *Generic processor*
- **CMake** (.cmake) - *Generic processor*

### 📝 Documentation (2 languages)
Languages for documentation and typesetting:
- **Markdown** (.md, .markdown) - *Generic processor*
- **TeX** (.tex) - *Generic processor*

### ⚙️ Assembly (1 language)
Low-level assembly languages:
- **Assembly** (.asm, .s) - *Generic processor*

### 🔧 Other (3 languages)
Additional programming languages:
- **C#** (.cs) - *Specialized processor*
- **Pascal** (.pas) - *Generic processor*
- **Tcl** (.tcl) - *Generic processor*

## Processor Types

### Specialized Processors (Priority 1-2)
High-performance, language-specific processors with optimized parsing:
- **C/C++**, **Python**, **C#** (Priority 1)
- **JavaScript**, **TypeScript**, **Metta**, **Rust**, **Go**, **Java** (Priority 2)

### Generic Processors (Priority 3-4)
Universal processors that handle multiple languages with Tree-sitter support:
- **Priority 3**: HTML, CSS, Ruby, Bash, SQL, Swift, Kotlin, Scala, Prolog
- **Priority 4**: All other languages

## Tree-sitter Support

**25 languages** have dedicated Tree-sitter parsers for enhanced syntax analysis:
- C/C++, Python, C#, JavaScript, TypeScript, HTML, CSS, PHP, Rust, Go, Swift
- Kotlin, Java, Scala, Haskell, Erlang, Elixir, Clojure, Ruby, Perl, Lua
- Bash, SQL, JSON, YAML, Markdown

## API Endpoints

### Get All Languages
```bash
GET /api/languages
```
Returns comprehensive information about all supported languages.

### Get Language List
```bash
GET /api/languages/list
```
Returns simple list of supported language names.

### Get Specific Language
```bash
GET /api/languages/{language_name}
```
Returns detailed information about a specific language.

### Get Languages by Category
```bash
GET /api/languages/category/{category}
```
Returns all languages in a specific category.

**Available categories:**
- `systems_programming`
- `scripting_languages` 
- `web_technologies`
- `jvm_languages`
- `functional_languages`
- `ai_logic_programming`
- `hardware_description`
- `data_languages`
- `scientific_computing`
- `build_systems`
- `documentation`
- `assembly`
- `other`

## Example API Usage

```bash
# Get all Python information
curl http://home-ai-server.local:5002/api/languages/python

# Get all web technologies
curl http://home-ai-server.local:5002/api/languages/category/web_technologies

# Get complete language overview
curl http://home-ai-server.local:5002/api/languages
```

## Language Detection

The system automatically detects programming languages based on:
1. **File extensions** (primary method)
2. **Content analysis** using Tree-sitter parsers
3. **Syntax patterns** for language-specific constructs
4. **Metadata hints** from file headers and shebangs

## Adding New Languages

To add support for a new programming language:
1. Create a language processor implementing `LanguageProcessor` interface
2. Register the processor with the language framework
3. Add file extension mappings
4. Optionally add Tree-sitter parser support
5. Update language categorization

The system is designed to be easily extensible for new programming languages and paradigms.
