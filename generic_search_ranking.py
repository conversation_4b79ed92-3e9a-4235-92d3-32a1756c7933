"""
Generic Search Ranking System
Language-agnostic search ranking that works across all codebases and programming languages
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ContentType(Enum):
    """Generic content types detected across all languages"""
    IMPLEMENTATION = "implementation"      # Actual code implementation
    DECLARATION = "declaration"           # Function/class declarations
    DOCUMENTATION = "documentation"       # Comments, docstrings
    CONFIGURATION = "configuration"       # Config files, settings
    METADATA = "metadata"                # System metadata, overviews
    INTERFACE = "interface"              # API definitions, interfaces

class QueryIntent(Enum):
    """User intent behind the query"""
    FIND_IMPLEMENTATION = "find_implementation"  # Looking for actual code
    UNDERSTAND_API = "understand_api"           # Looking for usage/docs
    EXPLORE_ARCHITECTURE = "explore_architecture" # Looking for design/structure
    DEBUG_ISSUE = "debug_issue"                 # Looking for problem solving
    LEARN_CONCEPT = "learn_concept"             # Looking for explanations

@dataclass
class GenericSearchContext:
    """Generic search context that works across all languages"""
    query: str
    intent: QueryIntent
    target_identifiers: List[str]  # Functions, classes, variables, etc.
    query_terms: List[str]         # Meaningful words from query
    language_hints: List[str]      # Programming language hints
    complexity_preference: str     # 'simple', 'detailed', 'comprehensive'

class GenericSearchRanker:
    """Universal search ranking system that works across all programming languages and codebases"""
    
    def __init__(self):
        # Generic patterns that work across languages
        self.implementation_indicators = [
            r'\{[^}]*\}',                    # Code blocks with braces
            r':\s*$.*?^(?=\S)',             # Python-style indented blocks
            r'return\s+\w+',                # Return statements
            r'if\s*\([^)]*\)',              # Conditional statements
            r'for\s*\([^)]*\)',             # For loops
            r'while\s*\([^)]*\)',           # While loops
            r'=\s*[^=]',                    # Assignment operations
            r'print\s*\(',                  # Print/output statements
            r'console\.log\s*\(',           # JavaScript console.log
            r'System\.out\.print',          # Java print
            r'std::cout',                   # C++ output
            r'fmt\.Print',                  # Go print
            r'println!',                    # Rust println
        ]
        
        self.declaration_indicators = [
            r'def\s+\w+\s*\(',              # Python function def
            r'function\s+\w+\s*\(',         # JavaScript function
            r'fn\s+\w+\s*\(',               # Rust function
            r'func\s+\w+\s*\(',             # Go function
            r'public\s+\w+\s+\w+\s*\(',     # Java/C# method
            r'class\s+\w+',                 # Class declarations
            r'interface\s+\w+',             # Interface declarations
            r'struct\s+\w+',                # Struct declarations
            r'enum\s+\w+',                  # Enum declarations
        ]
        
        self.documentation_indicators = [
            r'/\*\*.*?\*/',                 # JavaDoc style
            r'""".*?"""',                   # Python docstring
            r"'''.*?'''",                   # Python docstring
            r'//.*$',                       # Single line comments
            r'#.*$',                        # Python/shell comments
            r'<!--.*?-->',                  # HTML comments
        ]
        
        # Base ranking weights (can be adjusted per query)
        self.base_weights = {
            'exact_identifier_match': 10.0,
            'partial_identifier_match': 6.0,
            'implementation_content': 4.0,
            'declaration_content': 3.0,
            'documentation_content': 2.0,
            'query_term_density': 2.5,
            'language_match': 1.5,
            'content_quality': 1.2,
        }
        
        # Penalties for less relevant content
        self.penalties = {
            'metadata_only': -3.0,
            'architectural_overview': -2.0,
            'too_generic': -1.5,
            'wrong_language': -1.0,
        }

    def classify_query(self, query: str) -> GenericSearchContext:
        """Classify query intent and extract context - works for any language/codebase"""
        query_lower = query.lower()
        
        # Determine user intent
        intent = QueryIntent.FIND_IMPLEMENTATION  # Default
        
        if any(word in query_lower for word in ['how', 'usage', 'example', 'api', 'documentation']):
            intent = QueryIntent.UNDERSTAND_API
        elif any(word in query_lower for word in ['architecture', 'design', 'structure', 'overview']):
            intent = QueryIntent.EXPLORE_ARCHITECTURE
        elif any(word in query_lower for word in ['error', 'bug', 'debug', 'fix', 'problem']):
            intent = QueryIntent.DEBUG_ISSUE
        elif any(word in query_lower for word in ['what', 'explain', 'understand', 'concept']):
            intent = QueryIntent.LEARN_CONCEPT
        
        # Extract identifiers (generic approach)
        identifiers = self._extract_identifiers(query)
        
        # Extract meaningful query terms
        query_terms = self._extract_meaningful_terms(query)
        
        # Detect language hints
        language_hints = self._detect_language_hints(query)
        
        # Determine complexity preference
        complexity = 'detailed'  # Default
        if any(word in query_lower for word in ['simple', 'basic', 'quick']):
            complexity = 'simple'
        elif any(word in query_lower for word in ['comprehensive', 'complete', 'full']):
            complexity = 'comprehensive'
        
        return GenericSearchContext(
            query=query,
            intent=intent,
            target_identifiers=identifiers,
            query_terms=query_terms,
            language_hints=language_hints,
            complexity_preference=complexity
        )

    def rank_results(self, results: List[Dict[str, Any]], context: GenericSearchContext) -> List[Dict[str, Any]]:
        """Rank search results based on generic relevance scoring"""
        if not results:
            return results
        
        scored_results = []
        
        for result in results:
            score = self._calculate_generic_score(result, context)
            content_type = self._classify_content_type(result)
            
            scored_results.append({
                'result': result,
                'relevance_score': score,
                'content_type': content_type,
                'ranking_explanation': self._explain_score(result, context, score)
            })
        
        # Sort by relevance score
        scored_results.sort(key=lambda x: x['relevance_score'], reverse=True)
        
        # Log top results for debugging
        self._log_ranking_decision(scored_results[:3], context)
        
        return [item['result'] for item in scored_results]

    def _calculate_generic_score(self, result: Dict[str, Any], context: GenericSearchContext) -> float:
        """Calculate relevance score using generic, language-agnostic approach"""
        content = result.get('content', '').lower()
        metadata = result.get('metadata', {})
        
        base_score = 1.0
        
        # 1. Exact identifier matches (highest priority)
        for identifier in context.target_identifiers:
            if identifier.lower() in content:
                # Check if it's a definition/declaration (higher score)
                if re.search(rf'\b{re.escape(identifier)}\s*[\(\{{:]', content, re.IGNORECASE):
                    base_score *= self.base_weights['exact_identifier_match']
                else:
                    base_score *= self.base_weights['partial_identifier_match']
        
        # 2. Query term density
        term_matches = sum(1 for term in context.query_terms if term in content)
        if term_matches > 0:
            density = term_matches / len(context.query_terms) if context.query_terms else 0
            base_score *= (1.0 + density * self.base_weights['query_term_density'])
        
        # 3. Content type scoring based on intent
        content_type = self._classify_content_type(result)
        base_score *= self._get_intent_weight(content_type, context.intent)
        
        # 4. Language preference
        result_language = str(metadata.get('language', '')).lower()
        if context.language_hints and result_language in context.language_hints:
            base_score *= self.base_weights['language_match']
        
        # 5. Apply penalties
        base_score += self._calculate_generic_penalties(result, context)
        
        return max(base_score, 0.1)  # Minimum score

    def _classify_content_type(self, result: Dict[str, Any]) -> ContentType:
        """Classify content type using generic patterns"""
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        # Check for implementation code
        if any(re.search(pattern, content, re.MULTILINE | re.DOTALL) for pattern in self.implementation_indicators):
            return ContentType.IMPLEMENTATION
        
        # Check for declarations
        if any(re.search(pattern, content, re.IGNORECASE) for pattern in self.declaration_indicators):
            return ContentType.DECLARATION
        
        # Check for documentation
        if any(re.search(pattern, content, re.MULTILINE | re.DOTALL) for pattern in self.documentation_indicators):
            return ContentType.DOCUMENTATION
        
        # Check metadata indicators
        if str(metadata.get('language', '')).lower() == 'multi' or 'architectural' in str(metadata.get('chunk_type', '')):
            return ContentType.METADATA
        
        return ContentType.INTERFACE  # Default

    def _get_intent_weight(self, content_type: ContentType, intent: QueryIntent) -> float:
        """Get weight multiplier based on content type and user intent"""
        intent_weights = {
            QueryIntent.FIND_IMPLEMENTATION: {
                ContentType.IMPLEMENTATION: 3.0,
                ContentType.DECLARATION: 2.0,
                ContentType.DOCUMENTATION: 1.0,
                ContentType.METADATA: 0.3,
                ContentType.INTERFACE: 1.5,
            },
            QueryIntent.UNDERSTAND_API: {
                ContentType.DOCUMENTATION: 3.0,
                ContentType.DECLARATION: 2.5,
                ContentType.INTERFACE: 2.0,
                ContentType.IMPLEMENTATION: 1.5,
                ContentType.METADATA: 0.5,
            },
            QueryIntent.EXPLORE_ARCHITECTURE: {
                ContentType.METADATA: 3.0,
                ContentType.DOCUMENTATION: 2.0,
                ContentType.INTERFACE: 2.0,
                ContentType.DECLARATION: 1.5,
                ContentType.IMPLEMENTATION: 1.0,
            },
            QueryIntent.DEBUG_ISSUE: {
                ContentType.IMPLEMENTATION: 3.0,
                ContentType.DOCUMENTATION: 2.0,
                ContentType.DECLARATION: 1.5,
                ContentType.INTERFACE: 1.0,
                ContentType.METADATA: 0.5,
            },
            QueryIntent.LEARN_CONCEPT: {
                ContentType.DOCUMENTATION: 3.0,
                ContentType.IMPLEMENTATION: 2.0,
                ContentType.DECLARATION: 1.5,
                ContentType.INTERFACE: 1.5,
                ContentType.METADATA: 1.0,
            }
        }
        
        return intent_weights.get(intent, {}).get(content_type, 1.0)

    def _extract_identifiers(self, query: str) -> List[str]:
        """Extract identifiers using generic patterns that work across languages"""
        patterns = [
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\s*\(',           # function_name(
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)\.[a-zA-Z_]',     # object.method
            r'\b([a-zA-Z_][a-zA-Z0-9_]*)::[a-zA-Z_]',     # namespace::function
            r'(?:class|struct|interface|enum)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # type definitions
            r'(?:def|function|fn|func|method)\s+([a-zA-Z_][a-zA-Z0-9_]*)',  # function definitions
        ]
        
        identifiers = []
        for pattern in patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            identifiers.extend(matches)
        
        # Also extract standalone identifiers that look like code
        standalone = re.findall(r'\b([a-zA-Z_][a-zA-Z0-9_]{2,})\b', query)
        identifiers.extend([id for id in standalone if not id.lower() in ['function', 'method', 'class', 'the', 'and', 'for']])
        
        return list(set(identifiers))

    def _extract_meaningful_terms(self, query: str) -> List[str]:
        """Extract meaningful terms from query, filtering out noise"""
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'}
        
        words = re.findall(r'\b[a-zA-Z_][a-zA-Z0-9_]*\b', query.lower())
        return [word for word in words if word not in stop_words and len(word) > 2]

    def _detect_language_hints(self, query: str) -> List[str]:
        """Detect programming language hints in query"""
        language_indicators = {
            'python': ['python', 'py', 'def', 'import', 'django', 'flask'],
            'javascript': ['javascript', 'js', 'node', 'react', 'vue', 'function'],
            'java': ['java', 'class', 'public', 'spring', 'maven'],
            'c': ['c language', 'malloc', 'stdio', 'gcc'],
            'cpp': ['c++', 'cpp', 'std::', 'iostream'],
            'csharp': ['c#', 'csharp', '.net', 'dotnet', 'using'],
            'go': ['golang', 'go', 'func', 'package'],
            'rust': ['rust', 'fn', 'cargo', 'println!'],
            'typescript': ['typescript', 'ts', 'interface'],
        }
        
        query_lower = query.lower()
        detected = []
        
        for lang, indicators in language_indicators.items():
            if any(indicator in query_lower for indicator in indicators):
                detected.append(lang)
        
        return detected

    def _calculate_generic_penalties(self, result: Dict[str, Any], context: GenericSearchContext) -> float:
        """Calculate penalties for less relevant content"""
        penalty = 0.0
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        # Penalty for metadata-only content when looking for implementation
        if context.intent == QueryIntent.FIND_IMPLEMENTATION:
            if str(metadata.get('language', '')).lower() == 'multi':
                penalty += self.penalties['metadata_only']
            
            # Penalty for architectural overviews
            if 'architectural' in str(metadata.get('chunk_type', '')).lower():
                penalty += self.penalties['architectural_overview']
        
        # Penalty for content that's too generic (many function names but no implementation)
        function_count = len(re.findall(r'\b\w+\s*\(', content))
        if function_count > 15 and not any(re.search(pattern, content) for pattern in self.implementation_indicators):
            penalty += self.penalties['too_generic']
        
        return penalty

    def _explain_score(self, result: Dict[str, Any], context: GenericSearchContext, score: float) -> Dict[str, Any]:
        """Provide explanation for scoring decision"""
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        return {
            'final_score': round(score, 2),
            'content_type': self._classify_content_type(result).value,
            'identifier_matches': len([id for id in context.target_identifiers if id.lower() in content.lower()]),
            'query_term_matches': len([term for term in context.query_terms if term in content.lower()]),
            'language': metadata.get('language', 'unknown'),
            'intent_alignment': context.intent.value
        }

    def _log_ranking_decision(self, top_results: List[Dict[str, Any]], context: GenericSearchContext):
        """Log ranking decisions for debugging"""
        logger.info(f"🎯 Generic ranking for query: '{context.query}' (intent: {context.intent.value})")
        
        for i, item in enumerate(top_results):
            explanation = item['ranking_explanation']
            logger.info(f"  #{i+1}: Score={explanation['final_score']}, "
                       f"Type={explanation['content_type']}, "
                       f"Matches={explanation['identifier_matches']}, "
                       f"Lang={explanation['language']}")

# Global instance
generic_ranker = GenericSearchRanker()
