"""
Enhanced Search Ranking System
Addresses issues with architectural chunks being prioritized over specific function implementations
"""

import re
import json
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import logging
from ranking_config import ranking_config_manager, SearchScenario

logger = logging.getLogger(__name__)

class QueryType(Enum):
    """Types of queries for different ranking strategies"""
    FUNCTION_SPECIFIC = "function_specific"
    MEMORY_MANAGEMENT = "memory_management"
    ARCHITECTURAL = "architectural"
    SYSTEM_OVERVIEW = "system_overview"
    GENERAL = "general"

class ChunkRelevanceType(Enum):
    """Chunk relevance types for ranking"""
    EXACT_FUNCTION_MATCH = "exact_function_match"
    RELATED_FUNCTION = "related_function"
    IMPLEMENTATION_CODE = "implementation_code"
    ARCHITECTURAL_OVERVIEW = "architectural_overview"
    SYSTEM_METADATA = "system_metadata"

@dataclass
class SearchContext:
    """Context information for search ranking"""
    query: str
    query_type: QueryType
    target_functions: List[str]
    target_keywords: List[str]
    language_hints: List[str]
    prefer_implementations: bool = True

class EnhancedSearchRanker:
    """Enhanced search ranking system that prioritizes specific implementations over architectural overviews"""

    def __init__(self, use_adaptive_profiles: bool = True):
        self.use_adaptive_profiles = use_adaptive_profiles
        self.config_manager = ranking_config_manager

        # Default configuration (fallback)
        self.function_keywords = {
            'memory_management': [
                'alloc', 'malloc', 'calloc', 'realloc', 'free', 'tmwmem_alloc', 'tmwmem_free',
                'tmwmem_lowAlloc', 'tmwmem_lowFree', 'tmwmem_init', 'tmwmem_close',
                'memory_pool', 'buffer_management', 'heap', 'stack'
            ],
            'string_operations': [
                'strcpy', 'strcat', 'strlen', 'strcmp', 'strncmp', 'sprintf', 'snprintf'
            ],
            'file_operations': [
                'fopen', 'fclose', 'fread', 'fwrite', 'fprintf', 'fscanf'
            ],
            'network_operations': [
                'socket', 'bind', 'listen', 'accept', 'connect', 'send', 'recv'
            ]
        }

        # Default ranking weights (will be overridden by profiles)
        self.ranking_weights = {
            'exact_function_match': 10.0,
            'function_name_in_content': 5.0,
            'related_function': 3.0,
            'implementation_code': 2.5,
            'has_function_signature': 2.0,
            'specific_language': 1.8,
            'has_documentation': 1.5,
            'code_quality': 1.3,
            'architectural_overview': 0.5,
            'system_metadata': 0.3
        }

        # Default penalties (will be overridden by profiles)
        self.ranking_penalties = {
            'multi_language_chunk': -2.0,
            'architectural_pattern': -1.5,
            'too_many_functions': -1.0,
            'generic_metadata': -2.5
        }

    def classify_query(self, query: str) -> SearchContext:
        """Classify query and extract search context"""
        query_lower = query.lower()
        
        # Detect query type
        query_type = QueryType.GENERAL
        if any(keyword in query_lower for keyword in ['memory', 'alloc', 'free', 'malloc']):
            query_type = QueryType.MEMORY_MANAGEMENT
        elif any(keyword in query_lower for keyword in ['function', 'method', 'implement']):
            query_type = QueryType.FUNCTION_SPECIFIC
        elif any(keyword in query_lower for keyword in ['architecture', 'design', 'pattern', 'overview']):
            query_type = QueryType.ARCHITECTURAL
        elif any(keyword in query_lower for keyword in ['system', 'overall', 'structure']):
            query_type = QueryType.SYSTEM_OVERVIEW
        
        # Extract target functions
        target_functions = self._extract_function_names(query)
        
        # Extract keywords based on query type
        target_keywords = []
        if query_type == QueryType.MEMORY_MANAGEMENT:
            target_keywords = self.function_keywords['memory_management']
        
        # Extract language hints
        language_hints = self._extract_language_hints(query)
        
        return SearchContext(
            query=query,
            query_type=query_type,
            target_functions=target_functions,
            target_keywords=target_keywords,
            language_hints=language_hints,
            prefer_implementations=(query_type in [QueryType.FUNCTION_SPECIFIC, QueryType.MEMORY_MANAGEMENT])
        )

    def rank_search_results(self, results: List[Dict[str, Any]], search_context: SearchContext) -> List[Dict[str, Any]]:
        """Rank search results with enhanced relevance scoring"""
        if not results:
            return results

        # Get appropriate ranking profile for this query
        if self.use_adaptive_profiles:
            profile = self.config_manager.get_profile_for_query(search_context.query)
            # Update current weights and penalties from profile
            self.ranking_weights = profile.ranking_weights
            self.ranking_penalties = profile.ranking_penalties
            self.function_keywords.update(profile.function_keywords)
            logger.info(f"🎯 Using ranking profile: {profile.name} for query: '{search_context.query}'")

        scored_results = []

        for result in results:
            score = self._calculate_relevance_score(result, search_context)
            chunk_type = self._classify_chunk_relevance(result, search_context)

            scored_results.append({
                'result': result,
                'relevance_score': score,
                'chunk_type': chunk_type,
                'ranking_factors': self._get_ranking_explanation(result, search_context, score)
            })

        # Sort by relevance score (highest first)
        scored_results.sort(key=lambda x: x['relevance_score'] if isinstance(x['relevance_score'], (int, float)) else 0.0, reverse=True)

        # Log ranking decisions for debugging
        self._log_ranking_decisions(scored_results[:5], search_context)

        return [item['result'] for item in scored_results]

    def _calculate_relevance_score(self, result: Dict[str, Any], context: SearchContext) -> float:
        """Calculate comprehensive relevance score"""
        base_score = 1.0
        content = result.get('content', '').lower()
        metadata = result.get('metadata', {})
        
        # Extract metadata fields safely
        language = str(metadata.get('language', '')).lower()

        # Parse quality indicators if they exist
        quality_indicators = metadata.get('quality_indicators', {})
        if isinstance(quality_indicators, str):
            try:
                quality_indicators = json.loads(quality_indicators)
            except (json.JSONDecodeError, ValueError):
                quality_indicators = {}
        
        # 1. Exact function name matches (highest priority)
        for func_name in context.target_functions:
            if func_name.lower() in content:
                # Check if it's a function definition/declaration
                if re.search(rf'\b{re.escape(func_name)}\s*\(', content, re.IGNORECASE):
                    base_score *= self.ranking_weights['exact_function_match']
                else:
                    base_score *= self.ranking_weights['function_name_in_content']
        
        # 2. Target keyword matches
        keyword_matches = sum(1 for keyword in context.target_keywords if keyword in content)
        if keyword_matches > 0:
            base_score *= (1.0 + keyword_matches * 0.5)
        
        # 3. Implementation code indicators
        if self._is_implementation_code(content):
            base_score *= self.ranking_weights['implementation_code']
        
        # 4. Function signature detection
        if self._has_function_signatures(content):
            base_score *= self.ranking_weights['has_function_signature']
        
        # 5. Language specificity bonus
        if context.language_hints and language in context.language_hints:
            base_score *= self.ranking_weights['specific_language']
        
        # 6. Documentation bonus (but lower priority for function-specific queries)
        if quality_indicators.get('has_documentation', False):
            weight = self.ranking_weights['has_documentation']
            if context.prefer_implementations:
                weight *= 0.7  # Reduce documentation bonus for implementation queries
            base_score *= weight
        
        # 7. Apply penalties for broad/generic chunks
        base_score += self._calculate_penalties(result, context)
        
        return max(base_score, 0.1)  # Ensure minimum score

    def _calculate_penalties(self, result: Dict[str, Any], context: SearchContext) -> float:
        """Calculate penalties for broad/generic chunks"""
        penalty = 0.0
        content = result.get('content', '')
        metadata = result.get('metadata', {})
        
        language = str(metadata.get('language', '')).lower()
        chunk_type = str(metadata.get('chunk_type', '')).lower()
        
        # Penalty for multi-language chunks when looking for specific implementations
        if context.prefer_implementations and language in ['multi', 'system']:
            penalty += self.ranking_penalties['multi_language_chunk']
        
        # Penalty for architectural pattern chunks in function-specific queries
        if context.prefer_implementations and 'architectural' in chunk_type:
            penalty += self.ranking_penalties['architectural_pattern']
        
        # Penalty for chunks with too many function names (likely overviews)
        function_count = len(re.findall(r'\b\w+\s*\(', content))
        if function_count > 20:
            penalty += self.ranking_penalties['too_many_functions']
        
        # Penalty for generic metadata chunks
        if self._is_generic_metadata(result):
            penalty += self.ranking_penalties['generic_metadata']
        
        return penalty

    def _is_implementation_code(self, content: str) -> bool:
        """Check if content contains actual implementation code"""
        implementation_indicators = [
            r'\{[^}]*\}',  # Code blocks
            r'return\s+\w+',  # Return statements
            r'if\s*\(',  # Conditional statements
            r'for\s*\(',  # Loops
            r'while\s*\(',  # While loops
            r'=\s*[^=]',  # Assignments
        ]
        
        return any(re.search(pattern, content, re.IGNORECASE) for pattern in implementation_indicators)

    def _has_function_signatures(self, content: str) -> bool:
        """Check if content has function signatures/declarations"""
        # Look for function patterns like: type function_name(params)
        function_patterns = [
            r'\b\w+\s+\w+\s*\([^)]*\)\s*[{;]',  # C/C++ style
            r'def\s+\w+\s*\([^)]*\):',  # Python style
            r'function\s+\w+\s*\([^)]*\)',  # JavaScript style
        ]
        
        return any(re.search(pattern, content, re.IGNORECASE) for pattern in function_patterns)

    def _is_generic_metadata(self, result: Dict[str, Any]) -> bool:
        """Check if chunk is generic metadata rather than specific code"""
        content = result.get('content', '')
        
        # Check for metadata-like structures
        metadata_indicators = [
            'header_implementation_pairs',
            'interface_functions',
            'architectural_pattern',
            'system_overview'
        ]
        
        return any(indicator in content for indicator in metadata_indicators)

    def _classify_chunk_relevance(self, result: Dict[str, Any], context: SearchContext) -> ChunkRelevanceType:
        """Classify the relevance type of a chunk"""
        content = result.get('content', '').lower()
        metadata = result.get('metadata', {})
        
        # Check for exact function matches
        for func_name in context.target_functions:
            if re.search(rf'\b{re.escape(func_name)}\s*\(', content, re.IGNORECASE):
                return ChunkRelevanceType.EXACT_FUNCTION_MATCH
        
        # Check for implementation code
        if self._is_implementation_code(content):
            return ChunkRelevanceType.IMPLEMENTATION_CODE
        
        # Check for architectural overview
        if str(metadata.get('language', '')).lower() == 'multi':
            return ChunkRelevanceType.ARCHITECTURAL_OVERVIEW
        
        # Check for system metadata
        if self._is_generic_metadata(result):
            return ChunkRelevanceType.SYSTEM_METADATA
        
        return ChunkRelevanceType.RELATED_FUNCTION

    def _extract_function_names(self, query: str) -> List[str]:
        """Extract function names from query"""
        # Look for function-like patterns
        function_patterns = [
            r'\b(\w+)\s*\(',  # function_name(
            r'\b(\w+)\s+function',  # function_name function
            r'function\s+(\w+)',  # function function_name
        ]
        
        functions = []
        for pattern in function_patterns:
            matches = re.findall(pattern, query, re.IGNORECASE)
            functions.extend(matches)
        
        return list(set(functions))

    def _extract_language_hints(self, query: str) -> List[str]:
        """Extract programming language hints from query"""
        language_keywords = {
            'c': ['c language', 'malloc', 'stdio', 'tmwmem'],
            'cpp': ['c++', 'cpp', 'cxx', 'stl'],
            'python': ['python', 'py', 'def'],
            'javascript': ['javascript', 'js', 'function'],
            'java': ['java', 'class', 'public'],
            'csharp': ['c#', 'csharp', '.net']
        }
        
        query_lower = query.lower()
        detected = []
        
        for lang, keywords in language_keywords.items():
            if any(keyword in query_lower for keyword in keywords):
                detected.append(lang)
        
        return detected

    def _get_ranking_explanation(self, result: Dict[str, Any], context: SearchContext, score: float) -> Dict[str, Any]:
        """Generate explanation for ranking decision"""
        content = result.get('content', '').lower()
        metadata = result.get('metadata', {})
        
        factors = {
            'base_score': 1.0,
            'function_matches': len([f for f in context.target_functions if f.lower() in content]),
            'keyword_matches': sum(1 for k in context.target_keywords if k in content),
            'has_implementation': self._is_implementation_code(content),
            'has_function_signatures': self._has_function_signatures(content),
            'language': metadata.get('language', 'unknown'),
            'chunk_type': metadata.get('chunk_type', 'unknown'),
            'final_score': score
        }
        
        return factors

    def _log_ranking_decisions(self, top_results: List[Dict[str, Any]], context: SearchContext):
        """Log ranking decisions for debugging"""
        logger.info(f"🎯 Search ranking for query: '{context.query}' (type: {context.query_type.value})")
        
        for i, item in enumerate(top_results):
            factors = item['ranking_factors']
            logger.info(f"  #{i+1}: Score={factors['final_score']:.2f}, "
                       f"Lang={factors['language']}, Type={factors['chunk_type']}, "
                       f"FuncMatches={factors['function_matches']}, "
                       f"HasImpl={factors['has_implementation']}")
