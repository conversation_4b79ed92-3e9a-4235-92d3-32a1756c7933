{".class": "MypyFile", "_fullname": "framework_integration", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "Any": {".class": "SymbolTableNode", "cross_ref": "typing.Any", "kind": "Gdef"}, "BasicGPUManager": {".class": "SymbolTableNode", "cross_ref": "gpu_infrastructure.BasicGPUManager", "kind": "Gdef"}, "BasicProcessingCoordinator": {".class": "SymbolTableNode", "cross_ref": "gpu_infrastructure.BasicProcessingCoordinator", "kind": "Gdef"}, "ChunkGenerationStage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["processing_pipeline.ProcessingStage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "framework_integration.ChunkGenerationStage", "name": "ChunkGenerationStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "framework_integration.ChunkGenerationStage", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "framework_integration", "mro": ["framework_integration.ChunkGenerationStage", "processing_pipeline.ProcessingStage", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunk_registry", "llm_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.ChunkGenerationStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "chunk_registry", "llm_client"], "arg_types": ["framework_integration.ChunkGenerationStage", "chunk_system.ChunkGeneratorRegistry", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of ChunkGenerationStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_registry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.ChunkGenerationStage.chunk_registry", "name": "chunk_registry", "type": "chunk_system.ChunkGeneratorRegistry"}}, "get_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.ChunkGenerationStage.get_dependencies", "name": "get_dependencies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.ChunkGenerationStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dependencies of ChunkGenerationStage", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stage_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.ChunkGenerationStage.get_stage_description", "name": "get_stage_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.ChunkGenerationStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stage_description of ChunkGenerationStage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stage_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.ChunkGenerationStage.get_stage_name", "name": "get_stage_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.ChunkGenerationStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stage_name of ChunkGenerationStage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "llm_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.ChunkGenerationStage.llm_client", "name": "llm_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_data", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.ChunkGenerationStage.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_data", "context"], "arg_types": ["framework_integration.ChunkGenerationStage", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of ChunkGenerationStage", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "framework_integration.ChunkGenerationStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "framework_integration.ChunkGenerationStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "ChunkGeneratorRegistry": {".class": "SymbolTableNode", "cross_ref": "chunk_system.ChunkGeneratorRegistry", "kind": "Gdef"}, "CodeAnalysisFramework": {".class": "SymbolTableNode", "cross_ref": "language_framework.CodeAnalysisFramework", "kind": "Gdef"}, "CodeAnalysisStage": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["processing_pipeline.ProcessingStage"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "framework_integration.CodeAnalysisStage", "name": "CodeAnalysisStage", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "framework_integration.CodeAnalysisStage", "has_param_spec_type": false, "metaclass_type": "abc.ABCMeta", "metadata": {}, "module_name": "framework_integration", "mro": ["framework_integration.CodeAnalysisStage", "processing_pipeline.ProcessingStage", "abc.ABC", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "framework"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.CodeAnalysisStage.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "framework"], "arg_types": ["framework_integration.CodeAnalysisStage", "language_framework.CodeAnalysisFramework"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of CodeAnalysisStage", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "framework": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.CodeAnalysisStage.framework", "name": "framework", "type": "language_framework.CodeAnalysisFramework"}}, "get_dependencies": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.CodeAnalysisStage.get_dependencies", "name": "get_dependencies", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.CodeAnalysisStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_dependencies of CodeAnalysisStage", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_required_input_keys": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.CodeAnalysisStage.get_required_input_keys", "name": "get_required_input_keys", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.CodeAnalysisStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_required_input_keys of CodeAnalysisStage", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stage_description": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.CodeAnalysisStage.get_stage_description", "name": "get_stage_description", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.CodeAnalysisStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stage_description of CodeAnalysisStage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_stage_name": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.CodeAnalysisStage.get_stage_name", "name": "get_stage_name", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.CodeAnalysisStage"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_stage_name of CodeAnalysisStage", "ret_type": "builtins.str", "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "process": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_data", "context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.CodeAnalysisStage.process", "name": "process", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "input_data", "context"], "arg_types": ["framework_integration.CodeAnalysisStage", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process of CodeAnalysisStage", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "framework_integration.CodeAnalysisStage.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "framework_integration.CodeAnalysisStage", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Dict": {".class": "SymbolTableNode", "cross_ref": "typing.Dict", "kind": "Gdef"}, "GPUProcessingStage": {".class": "SymbolTableNode", "cross_ref": "gpu_infrastructure.GPUProcessingStage", "kind": "Gdef"}, "IntegratedCodeAnalysisSystem": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "framework_integration.IntegratedCodeAnalysisSystem", "name": "IntegratedCodeAnalysisSystem", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "framework_integration", "mro": ["framework_integration.IntegratedCodeAnalysisSystem", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "llm_client"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.__init__", "name": "__init__", "type": {".class": "CallableType", "arg_kinds": [0, 1], "arg_names": ["self", "llm_client"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "__init__ of IntegratedCodeAnalysisSystem", "ret_type": {".class": "NoneType"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_create_mock_llm_client": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem._create_mock_llm_client", "name": "_create_mock_llm_client", "type": null}}, "_create_processing_summary": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "results"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem._create_processing_summary", "name": "_create_processing_summary", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "results"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", {".class": "Instance", "args": ["builtins.str", "processing_pipeline.StageResult"], "extra_attrs": null, "type_ref": "builtins.dict"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_create_processing_summary of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_discover_files": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 0], "arg_names": ["self", "codebase_path", "patterns"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem._discover_files", "name": "_discover_files", "type": {".class": "CallableType", "arg_kinds": [0, 0, 0], "arg_names": ["self", "codebase_path", "patterns"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", "builtins.str", {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "_discover_files of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "_setup_pipeline": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem._setup_pipeline", "name": "_setup_pipeline", "type": null}}, "analyze_codebase": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "codebase_path", "file_patterns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.analyze_codebase", "name": "analyze_codebase", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "codebase_path", "file_patterns"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "analyze_codebase of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "chunk_registry": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.chunk_registry", "name": "chunk_registry", "type": "chunk_system.ChunkGeneratorRegistry"}}, "create_vector_database": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "codebase_path", "collection_name", "file_patterns"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.create_vector_database", "name": "create_vector_database", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1, 1], "arg_names": ["self", "codebase_path", "collection_name", "file_patterns"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", "builtins.str", {".class": "UnionType", "items": ["builtins.str", {".class": "NoneType"}], "uses_pep604_syntax": false}, {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str"], "extra_attrs": null, "type_ref": "builtins.list"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "create_vector_database of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "framework": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.framework", "name": "framework", "type": "language_framework.CodeAnalysisFramework"}}, "get_gpu_status": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.get_gpu_status", "name": "get_gpu_status", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_gpu_status of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_processing_recommendations": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "chunk_count"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.get_processing_recommendations", "name": "get_processing_recommendations", "type": {".class": "CallableType", "arg_kinds": [0, 0], "arg_names": ["self", "chunk_count"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", "builtins.int"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_processing_recommendations of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "get_system_info": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.get_system_info", "name": "get_system_info", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "get_system_info of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "gpu_manager": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.gpu_manager", "name": "gpu_manager", "type": "gpu_infrastructure.BasicGPUManager"}}, "gpu_stage": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.gpu_stage", "name": "gpu_stage", "type": "gpu_infrastructure.GPUProcessingStage"}}, "llm_client": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.llm_client", "name": "llm_client", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}}}, "pipeline": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.pipeline", "name": "pipeline", "type": "processing_pipeline.ProcessingPipeline"}}, "process_query": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "codebase_context"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.process_query", "name": "process_query", "type": {".class": "CallableType", "arg_kinds": [0, 0, 1], "arg_names": ["self", "query", "codebase_context"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem", "builtins.str", {".class": "UnionType", "items": [{".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, {".class": "NoneType"}], "uses_pep604_syntax": false}], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "process_query of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": [{".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}, {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}], "extra_attrs": null, "type_ref": "typing.Coroutine"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}, "processing_coordinator": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.processing_coordinator", "name": "processing_coordinator", "type": "gpu_infrastructure.BasicProcessingCoordinator"}}, "validate_system": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "framework_integration.IntegratedCodeAnalysisSystem.validate_system", "name": "validate_system", "type": {".class": "CallableType", "arg_kinds": [0], "arg_names": ["self"], "arg_types": ["framework_integration.IntegratedCodeAnalysisSystem"], "bound_args": [], "def_extras": {"first_arg": "self"}, "fallback": "builtins.function", "from_concatenate": false, "implicit": false, "imprecise_arg_kinds": false, "is_ellipsis_args": false, "name": "validate_system of IntegratedCodeAnalysisSystem", "ret_type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 2}], "extra_attrs": null, "type_ref": "builtins.dict"}, "type_guard": null, "type_is": null, "unpack_kwargs": false, "variables": []}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "framework_integration.IntegratedCodeAnalysisSystem.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "framework_integration.IntegratedCodeAnalysisSystem", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "List": {".class": "SymbolTableNode", "cross_ref": "typing.List", "kind": "Gdef"}, "MockLLMClient@237": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "framework_integration.MockLLMClient@237", "name": "MockLLMClient", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "framework_integration.MockLLMClient@237", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "framework_integration", "mro": ["framework_integration.MockLLMClient@237", "builtins.object"], "names": {".class": "SymbolTable", "generate": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "prompt"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "<EMAIL>", "name": "generate", "type": null}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "<EMAIL>", "id": 0, "name": "Self", "namespace": "", "upper_bound": "framework_integration.MockLLMClient@237", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Optional": {".class": "SymbolTableNode", "cross_ref": "typing.Optional", "kind": "Gdef"}, "Path": {".class": "SymbolTableNode", "cross_ref": "pathlib.Path", "kind": "Gdef"}, "ProcessingPipeline": {".class": "SymbolTableNode", "cross_ref": "processing_pipeline.ProcessingPipeline", "kind": "Gdef"}, "ProcessingStage": {".class": "SymbolTableNode", "cross_ref": "processing_pipeline.ProcessingStage", "kind": "Gdef"}, "StageResult": {".class": "SymbolTableNode", "cross_ref": "processing_pipeline.StageResult", "kind": "Gdef"}, "StageStatus": {".class": "SymbolTableNode", "cross_ref": "processing_pipeline.StageStatus", "kind": "Gdef"}, "TreeSitterChunker": {".class": "SymbolTableNode", "cross_ref": "tree_sitter_chunker.TreeSitterChunker", "kind": "Gdef"}, "VectorDBPipelineStage": {".class": "SymbolTableNode", "cross_ref": "vector_db_creator.VectorDBPipelineStage", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "framework_integration.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "framework_integration.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "framework_integration.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "framework_integration.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "framework_integration.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "framework_integration.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "create_default_chunk_registry": {".class": "SymbolTableNode", "cross_ref": "chunk_system.create_default_chunk_registry", "kind": "Gdef"}, "create_language_registry": {".class": "SymbolTableNode", "cross_ref": "language_registry.create_language_registry", "kind": "Gdef"}, "logger": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_inferred", "has_explicit_value"], "fullname": "framework_integration.logger", "name": "logger", "type": "logging.Logger"}}, "logging": {".class": "SymbolTableNode", "cross_ref": "logging", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "framework_integration.main", "name": "main", "type": null}}, "validate_language_coverage": {".class": "SymbolTableNode", "cross_ref": "language_registry.validate_language_coverage", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\framework_integration.py"}