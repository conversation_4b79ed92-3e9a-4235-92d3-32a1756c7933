"""
Integration module for enhanced search ranking
Integrates the enhanced ranking system with the existing code analyzer
"""

import logging
from typing import Dict, List, Any, Optional
from enhanced_search_ranking import EnhancedSearchRanker, SearchContext, QueryType

logger = logging.getLogger(__name__)

class SearchRankingIntegrator:
    """Integrates enhanced search ranking with existing search functionality"""
    
    def __init__(self):
        self.ranker = EnhancedSearchRanker()
        self.enabled = True
        
    def enhance_search_service(self, code_analyzer_service):
        """Enhance existing code analyzer service with improved ranking"""
        if not hasattr(code_analyzer_service, 'search'):
            logger.warning("Code analyzer service doesn't have search method")
            return code_analyzer_service
        
        # Store original search method
        original_search = code_analyzer_service.search
        
        def enhanced_search(query: str, codebase_name: str, n_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
            """Enhanced search with improved ranking"""
            try:
                # Get original search results
                results = original_search(query, codebase_name, n_results, **kwargs)
                
                if not self.enabled or not results:
                    return results
                
                # Apply enhanced ranking
                search_context = self.ranker.classify_query(query)
                enhanced_results = self.ranker.rank_search_results(results, search_context)
                
                logger.info(f"🎯 Enhanced ranking applied to {len(results)} results for query: '{query}'")
                
                return enhanced_results
                
            except Exception as e:
                logger.error(f"Enhanced ranking failed, falling back to original: {e}")
                return original_search(query, codebase_name, n_results, **kwargs)
        
        # Replace the search method
        code_analyzer_service.search = enhanced_search
        code_analyzer_service.search_ranker = self.ranker
        
        logger.info("✅ Enhanced search ranking integrated successfully")
        return code_analyzer_service
    
    def enhance_main_search_endpoint(self, app):
        """Enhance the main search endpoint in main.py"""
        
        # Find and enhance the search endpoint
        for route in app.routes:
            if hasattr(route, 'path') and '/tools/search' in route.path:
                logger.info("Found search endpoint, enhancing with improved ranking")
                break
        
        return app
    
    def create_ranking_configuration_endpoint(self, app):
        """Add endpoint to configure ranking parameters"""
        from fastapi import Body
        from fastapi.responses import JSONResponse
        
        @app.post("/api/search/configure_ranking")
        async def configure_search_ranking(config: Dict[str, Any] = Body(...)):
            """Configure search ranking parameters"""
            try:
                if 'enabled' in config:
                    self.enabled = config['enabled']
                
                if 'ranking_weights' in config:
                    self.ranker.ranking_weights.update(config['ranking_weights'])
                
                if 'ranking_penalties' in config:
                    self.ranker.ranking_penalties.update(config['ranking_penalties'])
                
                return JSONResponse(content={
                    "success": True,
                    "message": "Search ranking configuration updated",
                    "current_config": {
                        "enabled": self.enabled,
                        "ranking_weights": self.ranker.ranking_weights,
                        "ranking_penalties": self.ranker.ranking_penalties
                    }
                })
                
            except Exception as e:
                return JSONResponse(content={
                    "success": False,
                    "error": f"Failed to configure ranking: {str(e)}"
                }, status_code=500)
        
        @app.get("/api/search/ranking_status")
        async def get_ranking_status():
            """Get current ranking configuration"""
            return JSONResponse(content={
                "enabled": self.enabled,
                "ranking_weights": self.ranker.ranking_weights,
                "ranking_penalties": self.ranker.ranking_penalties,
                "function_keywords": self.ranker.function_keywords
            })
        
        logger.info("✅ Added search ranking configuration endpoints")

def integrate_enhanced_ranking(code_analyzer_service, app=None):
    """Main integration function"""
    integrator = SearchRankingIntegrator()
    
    # Enhance the service
    enhanced_service = integrator.enhance_search_service(code_analyzer_service)
    
    # Add configuration endpoints if app is provided
    if app:
        integrator.create_ranking_configuration_endpoint(app)
    
    return enhanced_service, integrator

# Chunk filtering improvements
class ChunkFilter:
    """Filter chunks to reduce noise from architectural overviews"""
    
    def __init__(self):
        self.architectural_indicators = [
            'header_implementation_pairs',
            'interface_functions',
            'architectural_pattern',
            'system_design'
        ]
    
    def filter_chunks_for_function_queries(self, chunks: List[Dict[str, Any]], query: str) -> List[Dict[str, Any]]:
        """Filter out broad architectural chunks for function-specific queries"""
        query_lower = query.lower()
        
        # Check if this is a function-specific query
        is_function_query = any(keyword in query_lower for keyword in [
            'function', 'method', 'implement', 'alloc', 'free', 'malloc'
        ])
        
        if not is_function_query:
            return chunks
        
        filtered_chunks = []
        
        for chunk in chunks:
            content = chunk.get('content', '')
            metadata = chunk.get('metadata', {})
            
            # Skip architectural overview chunks for function queries
            if self._is_architectural_overview(content, metadata):
                continue
            
            # Skip chunks with too many function references (likely overviews)
            if self._has_too_many_functions(content):
                continue
            
            filtered_chunks.append(chunk)
        
        logger.info(f"🔍 Filtered {len(chunks)} -> {len(filtered_chunks)} chunks for function query")
        return filtered_chunks
    
    def _is_architectural_overview(self, content: str, metadata: Dict[str, Any]) -> bool:
        """Check if chunk is an architectural overview"""
        # Check metadata
        language = str(metadata.get('language', '')).lower()
        chunk_type = str(metadata.get('chunk_type', '')).lower()
        
        if language in ['multi', 'system'] and 'architectural' in chunk_type:
            return True
        
        # Check content for architectural indicators
        return any(indicator in content for indicator in self.architectural_indicators)
    
    def _has_too_many_functions(self, content: str, threshold: int = 15) -> bool:
        """Check if chunk has too many function references (likely an overview)"""
        import re
        function_count = len(re.findall(r'\b\w+\s*\(', content))
        return function_count > threshold

# Query preprocessing improvements
class QueryPreprocessor:
    """Preprocess queries to improve search accuracy"""
    
    def __init__(self):
        self.function_synonyms = {
            'memory management': ['tmwmem_alloc', 'tmwmem_free', 'malloc', 'free'],
            'allocation': ['alloc', 'malloc', 'tmwmem_alloc'],
            'deallocation': ['free', 'tmwmem_free'],
            'initialization': ['init', 'initialize', 'tmwmem_init']
        }
    
    def preprocess_query(self, query: str) -> Dict[str, Any]:
        """Preprocess query to extract better search terms"""
        original_query = query
        enhanced_terms = []
        
        # Expand synonyms
        query_lower = query.lower()
        for concept, synonyms in self.function_synonyms.items():
            if concept in query_lower:
                enhanced_terms.extend(synonyms)
        
        # Extract specific function names
        import re
        function_matches = re.findall(r'\b(\w+_\w+)\b', query)  # Match function_name patterns
        enhanced_terms.extend(function_matches)
        
        return {
            'original_query': original_query,
            'enhanced_terms': list(set(enhanced_terms)),
            'expanded_query': f"{query} {' '.join(enhanced_terms)}" if enhanced_terms else query
        }

def create_search_enhancement_wrapper(code_analyzer_service):
    """Create a comprehensive search enhancement wrapper"""
    
    # Initialize components
    ranker = EnhancedSearchRanker()
    chunk_filter = ChunkFilter()
    query_preprocessor = QueryPreprocessor()
    
    # Store original methods
    original_search = code_analyzer_service.search
    
    def enhanced_search_with_filtering(query: str, codebase_name: str, n_results: int = 10, **kwargs) -> List[Dict[str, Any]]:
        """Comprehensive enhanced search with preprocessing, filtering, and ranking"""
        try:
            # 1. Preprocess query
            query_data = query_preprocessor.preprocess_query(query)
            search_query = query_data['expanded_query']
            
            logger.info(f"🔍 Query preprocessing: '{query}' -> '{search_query}'")
            
            # 2. Get original search results with expanded query
            results = original_search(search_query, codebase_name, n_results * 2, **kwargs)  # Get more results for filtering
            
            if not results:
                return results
            
            # 3. Filter chunks to reduce noise
            filtered_results = chunk_filter.filter_chunks_for_function_queries(results, query)
            
            # 4. Apply enhanced ranking
            search_context = ranker.classify_query(query)
            ranked_results = ranker.rank_search_results(filtered_results, search_context)
            
            # 5. Limit to requested number of results
            final_results = ranked_results[:n_results]
            
            logger.info(f"🎯 Enhanced search: {len(results)} -> {len(filtered_results)} -> {len(final_results)} results")
            
            return final_results
            
        except Exception as e:
            logger.error(f"Enhanced search failed, falling back to original: {e}")
            return original_search(query, codebase_name, n_results, **kwargs)
    
    # Replace the search method
    code_analyzer_service.search = enhanced_search_with_filtering
    code_analyzer_service.search_ranker = ranker
    code_analyzer_service.chunk_filter = chunk_filter
    code_analyzer_service.query_preprocessor = query_preprocessor
    
    logger.info("✅ Comprehensive search enhancement applied")
    return code_analyzer_service
